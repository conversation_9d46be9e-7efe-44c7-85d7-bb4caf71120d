"""
FastAPI Main Application for Contextual AI-Powered Product Search Engine.
Provides REST API endpoints for product search with natural language processing.
"""

import logging
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from search_engine import SearchEngine
from memory_store import MemoryStore

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Walmart-Style Product Search API",
    description="Contextual AI-powered product search engine with natural language processing",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize components
search_engine = None
memory_store = None

@app.on_event("startup")
async def startup_event():
    """Initialize search engine and memory store on startup."""
    global search_engine, memory_store

    try:
        logger.info("Initializing search engine...")
        search_engine = SearchEngine("products.json")

        logger.info("Initializing memory store...")
        memory_store = MemoryStore()

        logger.info("Application startup completed successfully")
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise


# Pydantic models for request/response
class SearchRequest(BaseModel):
    q: str = Field(..., description="Natural language search query", min_length=1)
    limit: Optional[int] = Field(10, description="Maximum number of results", ge=1, le=50)


class ChatSearchRequest(BaseModel):
    q: str = Field(..., description="Natural language search query", min_length=1)
    session_id: str = Field(..., description="Unique session identifier")
    limit: Optional[int] = Field(10, description="Maximum number of results", ge=1, le=50)


class SearchResponse(BaseModel):
    query: str
    intent: Dict[str, Any]
    total_results: int
    returned_results: int
    products: list
    search_metadata: Dict[str, Any]
    cached: bool = False
    session_id: Optional[str] = None


# Dependency to get search engine
def get_search_engine() -> SearchEngine:
    if search_engine is None:
        raise HTTPException(status_code=503, detail="Search engine not initialized")
    return search_engine


# Dependency to get memory store
def get_memory_store() -> MemoryStore:
    if memory_store is None:
        raise HTTPException(status_code=503, detail="Memory store not initialized")
    return memory_store


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Walmart-Style Product Search API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "endpoints": {
            "search": "/search",
            "chat_search": "/chat-search",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check(
    search_eng: SearchEngine = Depends(get_search_engine),
    mem_store: MemoryStore = Depends(get_memory_store)
):
    """Health check endpoint."""
    try:
        search_stats = search_eng.get_search_stats()
        cache_stats = mem_store.get_cache_stats()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "search_engine": search_stats,
            "memory_store": cache_stats,
            "api_version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.post("/search", response_model=SearchResponse)
async def search_products(
    request: SearchRequest,
    search_eng: SearchEngine = Depends(get_search_engine),
    mem_store: MemoryStore = Depends(get_memory_store)
):
    """
    Search products using natural language queries.

    This endpoint accepts natural language queries and returns relevant products.
    It uses Perplexity API for query understanding and caches results for performance.
    """
    try:
        logger.info(f"Search request: {request.q}")

        # Check cache first
        cached_response = mem_store.get_cached_response(request.q)
        if cached_response:
            logger.info("Returning cached response")
            cached_response['cached'] = True
            return SearchResponse(**cached_response)

        # Perform search
        search_results = search_eng.search(
            query=request.q,
            limit=request.limit
        )

        # Cache the response
        mem_store.cache_response(request.q, search_results)

        # Prepare response
        search_results['cached'] = False
        return SearchResponse(**search_results)

    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@app.post("/chat-search", response_model=SearchResponse)
async def chat_search_products(
    request: ChatSearchRequest,
    search_eng: SearchEngine = Depends(get_search_engine),
    mem_store: MemoryStore = Depends(get_memory_store)
):
    """
    Search products with session context for conversational search.

    This endpoint maintains session context to handle follow-up queries
    like "make it lightweight" or "show me cheaper options".
    """
    try:
        logger.info(f"Chat search request: {request.q} (session: {request.session_id})")

        # Get session context
        session_context = mem_store.get_session_context(request.session_id)

        # Build context string from conversation history
        context_parts = []
        if session_context.get('conversation_history'):
            # Get last few interactions for context
            recent_history = session_context['conversation_history'][-3:]
            for interaction in recent_history:
                context_parts.append(f"Previous query: {interaction['query']}")

        context_string = " | ".join(context_parts)

        # Check if this is a refinement query
        refinement_keywords = ['make', 'show', 'find', 'filter', 'cheaper', 'expensive', 'lighter', 'heavier']
        is_refinement = any(keyword in request.q.lower() for keyword in refinement_keywords)

        # If it's a refinement and we have previous context, combine queries
        full_query = request.q
        if is_refinement and session_context.get('last_query'):
            full_query = f"{session_context['last_query']} {request.q}"
            logger.info(f"Combined query for refinement: {full_query}")

        # Check cache for the full query
        cache_key = f"{request.session_id}:{full_query}"
        cached_response = mem_store.get_cached_response(cache_key)
        if cached_response:
            logger.info("Returning cached chat response")
            cached_response['cached'] = True
            cached_response['session_id'] = request.session_id
            return SearchResponse(**cached_response)

        # Perform search with context
        search_results = search_eng.search(
            query=full_query,
            context=context_string,
            limit=request.limit
        )

        # Update session context
        context_summary = f"Searched for: {full_query}"
        mem_store.update_session_context(
            session_id=request.session_id,
            query=request.q,
            response=search_results,
            context_summary=context_summary
        )

        # Cache the response with session-specific key
        mem_store.cache_response(cache_key, search_results)

        # Prepare response
        search_results['cached'] = False
        search_results['session_id'] = request.session_id
        return SearchResponse(**search_results)

    except Exception as e:
        logger.error(f"Chat search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat search failed: {str(e)}")


@app.get("/categories")
async def get_categories(search_eng: SearchEngine = Depends(get_search_engine)):
    """Get all available product categories and subcategories."""
    try:
        categories = search_eng.get_categories()
        return {
            "categories": categories,
            "total_categories": len(categories)
        }
    except Exception as e:
        logger.error(f"Failed to get categories: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")


@app.get("/brands")
async def get_brands(search_eng: SearchEngine = Depends(get_search_engine)):
    """Get all available brands."""
    try:
        brands = search_eng.get_brands()
        return {
            "brands": brands,
            "total_brands": len(brands)
        }
    except Exception as e:
        logger.error(f"Failed to get brands: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get brands: {str(e)}")


@app.get("/products/{product_id}")
async def get_product(
    product_id: str,
    search_eng: SearchEngine = Depends(get_search_engine)
):
    """Get a specific product by ID."""
    try:
        product = search_eng.get_product_by_id(product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")
        return product
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get product: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get product: {str(e)}")


@app.delete("/sessions/{session_id}")
async def clear_session(
    session_id: str,
    mem_store: MemoryStore = Depends(get_memory_store)
):
    """Clear a specific session's conversation history."""
    try:
        success = mem_store.clear_session(session_id)
        if success:
            return {"message": f"Session {session_id} cleared successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear session")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to clear session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear session: {str(e)}")


@app.get("/stats")
async def get_stats(
    search_eng: SearchEngine = Depends(get_search_engine),
    mem_store: MemoryStore = Depends(get_memory_store)
):
    """Get comprehensive API statistics."""
    try:
        search_stats = search_eng.get_search_stats()
        cache_stats = mem_store.get_cache_stats()

        return {
            "api_version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "search_engine": search_stats,
            "memory_store": cache_stats,
            "endpoints": {
                "total": 9,
                "search": "/search",
                "chat_search": "/chat-search",
                "health": "/health",
                "categories": "/categories",
                "brands": "/brands",
                "products": "/products/{id}",
                "clear_session": "/sessions/{id}",
                "stats": "/stats"
            }
        }
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"detail": "Endpoint not found", "available_endpoints": ["/", "/docs", "/health", "/search", "/chat-search"]}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "message": "Please try again later"}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )