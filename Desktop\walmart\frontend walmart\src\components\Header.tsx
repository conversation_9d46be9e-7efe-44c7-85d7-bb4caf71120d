
import { useState } from 'react';
import { Search, ShoppingCart, User, Menu, MapPin, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-walmart-blue text-white sticky top-0 z-50">
      {/* Top banner */}
      <div className="bg-walmart-darkblue py-1">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center text-xs">
          <div className="flex items-center space-x-4">
            <span>How do you want your items?</span>
            <div className="flex items-center space-x-1">
              <MapPin className="w-3 h-3" />
              <span>Sacramento, 95829</span>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <span>Registry</span>
            <span>Weekly Ad</span>
            <span>Walmart+</span>
          </div>
        </div>
      </div>

      {/* Main header */}
      <div className="py-3">
        <div className="max-w-7xl mx-auto px-4 flex items-center justify-between">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-white hover:bg-walmart-darkblue"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="w-5 h-5" />
          </Button>

          {/* Logo */}
          <div className="flex items-center">
            <div className="text-2xl font-bold">
              <span className="text-walmart-yellow">Walmart</span>
            </div>
          </div>

          {/* Search bar */}
          <div className="flex-1 max-w-2xl mx-8 hidden md:block">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search everything at Walmart online and in store"
                className="w-full pl-4 pr-12 py-3 rounded-full text-black border-0 focus:ring-2 focus:ring-walmart-yellow"
              />
              <Button
                size="sm"
                className="absolute right-1 top-1 bg-walmart-yellow hover:bg-yellow-500 text-black rounded-full px-4"
              >
                <Search className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Right icons */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" className="hidden md:flex flex-col items-center text-white hover:bg-walmart-darkblue">
              <Heart className="w-5 h-5" />
              <span className="text-xs">Reorder</span>
            </Button>
            
            <Button variant="ghost" size="sm" className="flex flex-col items-center text-white hover:bg-walmart-darkblue">
              <User className="w-5 h-5" />
              <span className="text-xs hidden md:block">Account</span>
            </Button>
            
            <Button variant="ghost" size="sm" className="flex flex-col items-center text-white hover:bg-walmart-darkblue relative">
              <ShoppingCart className="w-5 h-5" />
              <span className="text-xs hidden md:block">$0.00</span>
              <span className="absolute -top-1 -right-1 bg-walmart-yellow text-black text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">0</span>
            </Button>
          </div>
        </div>

        {/* Mobile search */}
        <div className="md:hidden mt-3 px-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search everything at Walmart"
              className="w-full pl-4 pr-12 py-2 rounded-full text-black border-0"
            />
            <Button
              size="sm"
              className="absolute right-1 top-1 bg-walmart-yellow hover:bg-yellow-500 text-black rounded-full px-3"
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Navigation menu */}
      <nav className="bg-white text-black py-2 shadow-sm">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center space-x-8 overflow-x-auto">
            <span className="font-medium whitespace-nowrap">Departments</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Services</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Grocery & Essentials</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Christmas Shop</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Fashion</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Electronics</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Home</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Auto</span>
            <span className="whitespace-nowrap hover:text-walmart-blue cursor-pointer">Pharmacy</span>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;
