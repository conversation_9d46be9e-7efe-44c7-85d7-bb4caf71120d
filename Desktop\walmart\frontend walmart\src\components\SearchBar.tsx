import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSearch } from '@/contexts/SearchContext';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  className?: string;
  placeholder?: string;
  onSearchSubmit?: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  className,
  placeholder = "Search everything at Walmart online and in store",
  onSearchSubmit,
}) => {
  const { searchState, searchProducts, setQuery } = useSearch();
  const [localQuery, setLocalQuery] = useState(searchState.query);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Popular search suggestions
  const popularSearches = [
    "iPhone 15",
    "Gaming laptop",
    "Air fryer",
    "Wireless headphones",
    "Running shoes",
    "Kitchen appliances",
    "Smart watch",
    "Yoga mat",
    "Books",
    "Instant pot"
  ];

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('walmart-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to parse recent searches:', error);
      }
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = (query: string) => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    const updated = [trimmedQuery, ...recentSearches.filter(s => s !== trimmedQuery)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('walmart-recent-searches', JSON.stringify(updated));
  };

  // Handle search submission
  const handleSearch = async (query: string = localQuery) => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    setShowSuggestions(false);
    saveRecentSearch(trimmedQuery);
    
    try {
      await searchProducts(trimmedQuery);
      onSearchSubmit?.(trimmedQuery);
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalQuery(value);
    setQuery(value);
    setShowSuggestions(value.length > 0);
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setLocalQuery(suggestion);
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  // Clear search
  const clearSearch = () => {
    setLocalQuery('');
    setQuery('');
    setShowSuggestions(false);
    searchInputRef.current?.focus();
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !searchInputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter suggestions based on input
  const filteredSuggestions = popularSearches.filter(search =>
    search.toLowerCase().includes(localQuery.toLowerCase())
  );

  return (
    <div className={cn("relative w-full max-w-2xl", className)}>
      <div className="relative">
        <Input
          ref={searchInputRef}
          type="text"
          value={localQuery}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          onFocus={() => setShowSuggestions(localQuery.length > 0 || recentSearches.length > 0)}
          placeholder={placeholder}
          className="w-full pl-4 pr-20 py-3 rounded-full text-black border-0 focus:ring-2 focus:ring-walmart-yellow bg-white"
          disabled={searchState.isLoading}
        />
        
        {/* Clear button */}
        {localQuery && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-12 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100 rounded-full"
          >
            <X className="w-4 h-4 text-gray-500" />
          </Button>
        )}

        {/* Search button */}
        <Button
          type="button"
          onClick={() => handleSearch()}
          disabled={searchState.isLoading || !localQuery.trim()}
          className="absolute right-1 top-1 bg-walmart-yellow hover:bg-yellow-500 text-black rounded-full px-4 disabled:opacity-50"
        >
          <Search className="w-4 h-4" />
        </Button>
      </div>

      {/* Search suggestions dropdown */}
      {showSuggestions && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-96 overflow-y-auto"
        >
          {/* Recent searches */}
          {recentSearches.length > 0 && (
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <Clock className="w-4 h-4" />
                <span>Recent searches</span>
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={`recent-${index}`}
                  onClick={() => handleSuggestionClick(search)}
                  className="block w-full text-left px-2 py-1 text-sm hover:bg-gray-50 rounded"
                >
                  {search}
                </button>
              ))}
            </div>
          )}

          {/* Popular/filtered suggestions */}
          {(localQuery.length === 0 || filteredSuggestions.length > 0) && (
            <div className="p-3">
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <TrendingUp className="w-4 h-4" />
                <span>{localQuery.length === 0 ? 'Popular searches' : 'Suggestions'}</span>
              </div>
              {(localQuery.length === 0 ? popularSearches : filteredSuggestions)
                .slice(0, 8)
                .map((search, index) => (
                  <button
                    key={`suggestion-${index}`}
                    onClick={() => handleSuggestionClick(search)}
                    className="block w-full text-left px-2 py-1 text-sm hover:bg-gray-50 rounded"
                  >
                    {search}
                  </button>
                ))}
            </div>
          )}

          {/* No suggestions */}
          {localQuery.length > 0 && filteredSuggestions.length === 0 && recentSearches.length === 0 && (
            <div className="p-3 text-sm text-gray-500 text-center">
              No suggestions found
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
