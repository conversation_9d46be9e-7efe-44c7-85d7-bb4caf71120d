import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { apiService, Product, SearchResponse, generateSessionId } from '@/lib/api';

interface SearchState {
  query: string;
  results: Product[];
  isLoading: boolean;
  error: string | null;
  totalResults: number;
  searchMetadata: Record<string, any>;
  sessionId: string;
  hasSearched: boolean;
}

interface SearchContextType {
  searchState: SearchState;
  searchProducts: (query: string, limit?: number) => Promise<void>;
  chatSearch: (query: string, limit?: number) => Promise<void>;
  clearSearch: () => void;
  clearSession: () => Promise<void>;
  setQuery: (query: string) => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

const initialSearchState: SearchState = {
  query: '',
  results: [],
  isLoading: false,
  error: null,
  totalResults: 0,
  searchMetadata: {},
  sessionId: generateSessionId(),
  hasSearched: false,
};

interface SearchProviderProps {
  children: ReactNode;
}

export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const [searchState, setSearchState] = useState<SearchState>(initialSearchState);

  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }));
  }, []);

  const searchProducts = useCallback(async (query: string, limit: number = 10) => {
    if (!query.trim()) {
      return;
    }

    setSearchState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      query: query.trim(),
    }));

    try {
      const response: SearchResponse = await apiService.searchProducts({
        q: query.trim(),
        limit,
      });

      setSearchState(prev => ({
        ...prev,
        results: response.products,
        totalResults: response.total_results,
        searchMetadata: response.search_metadata,
        isLoading: false,
        hasSearched: true,
      }));
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Search failed',
        isLoading: false,
        results: [],
        totalResults: 0,
      }));
    }
  }, []);

  const chatSearch = useCallback(async (query: string, limit: number = 10) => {
    if (!query.trim()) {
      return;
    }

    setSearchState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      query: query.trim(),
    }));

    try {
      const response: SearchResponse = await apiService.chatSearchProducts({
        q: query.trim(),
        session_id: searchState.sessionId,
        limit,
      });

      setSearchState(prev => ({
        ...prev,
        results: response.products,
        totalResults: response.total_results,
        searchMetadata: response.search_metadata,
        isLoading: false,
        hasSearched: true,
      }));
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Chat search failed',
        isLoading: false,
        results: [],
        totalResults: 0,
      }));
    }
  }, [searchState.sessionId]);

  const clearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      results: [],
      error: null,
      totalResults: 0,
      searchMetadata: {},
      hasSearched: false,
    }));
  }, []);

  const clearSession = useCallback(async () => {
    try {
      await apiService.clearSession(searchState.sessionId);
      setSearchState(prev => ({
        ...initialSearchState,
        sessionId: generateSessionId(),
      }));
    } catch (error) {
      console.error('Failed to clear session:', error);
      // Still reset the local state even if the API call fails
      setSearchState(prev => ({
        ...initialSearchState,
        sessionId: generateSessionId(),
      }));
    }
  }, [searchState.sessionId]);

  const contextValue: SearchContextType = {
    searchState,
    searchProducts,
    chatSearch,
    clearSearch,
    clearSession,
    setQuery,
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};

export const useSearch = (): SearchContextType => {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

export default SearchContext;
