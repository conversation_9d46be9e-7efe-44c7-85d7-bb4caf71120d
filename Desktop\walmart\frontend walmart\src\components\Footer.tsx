
import { Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-walmart-blue text-white">
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Customer Service */}
          <div>
            <h3 className="font-bold text-lg mb-4">Customer Service</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-walmart-yellow">Help Center</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Contact Us</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Returns & Exchanges</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Shipping Info</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Track Your Order</a></li>
            </ul>
          </div>

          {/* Shop */}
          <div>
            <h3 className="font-bold text-lg mb-4">Shop</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-walmart-yellow">All Departments</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Grocery</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Electronics</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Home</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Fashion</a></li>
            </ul>
          </div>

          {/* Account */}
          <div>
            <h3 className="font-bold text-lg mb-4">Account</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-walmart-yellow">Sign In</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Create Account</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Purchase History</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Lists</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Registry</a></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-bold text-lg mb-4">Services</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-walmart-yellow">Pharmacy</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Photo Center</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Auto Services</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">MoneyGram</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Walmart+</a></li>
            </ul>
          </div>

          {/* Get to Know Us */}
          <div>
            <h3 className="font-bold text-lg mb-4">Get to Know Us</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-walmart-yellow">About Walmart</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Careers</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">News</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Sustainability</a></li>
              <li><a href="#" className="hover:text-walmart-yellow">Store Locations</a></li>
            </ul>
          </div>
        </div>

        {/* Social Media & Apps */}
        <div className="border-t border-walmart-darkblue mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h4 className="font-semibold mb-3">Connect with us</h4>
              <div className="flex space-x-4">
                <Facebook className="w-6 h-6 hover:text-walmart-yellow cursor-pointer" />
                <Twitter className="w-6 h-6 hover:text-walmart-yellow cursor-pointer" />
                <Instagram className="w-6 h-6 hover:text-walmart-yellow cursor-pointer" />
                <Youtube className="w-6 h-6 hover:text-walmart-yellow cursor-pointer" />
              </div>
            </div>
            
            <div className="text-center md:text-right">
              <h4 className="font-semibold mb-3">Download our app</h4>
              <div className="flex space-x-4">
                <div className="bg-black text-white px-4 py-2 rounded text-sm cursor-pointer hover:bg-gray-800">
                  📱 App Store
                </div>
                <div className="bg-black text-white px-4 py-2 rounded text-sm cursor-pointer hover:bg-gray-800">
                  🤖 Google Play
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-walmart-darkblue mt-8 pt-6 text-center text-sm">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <span className="text-walmart-yellow font-bold text-lg">Walmart</span>
              <span className="ml-2">Save Money. Live Better.</span>
            </div>
            <div className="flex flex-wrap justify-center space-x-4 text-xs">
              <a href="#" className="hover:text-walmart-yellow">Privacy Policy</a>
              <a href="#" className="hover:text-walmart-yellow">Terms of Use</a>
              <a href="#" className="hover:text-walmart-yellow">CA Privacy Rights</a>
              <a href="#" className="hover:text-walmart-yellow">Your Privacy Choices</a>
            </div>
          </div>
          <div className="mt-4 text-xs text-gray-300">
            © 2024 Walmart Inc. All Rights Reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
