"""
Test script for the Walmart-Style Product Search API.
Tests all endpoints and validates functionality.
"""

import requests
import json
import time
import uuid
from typing import Dict, Any


class APITester:
    """Test class for the Product Search API."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = str(uuid.uuid4())
        self.test_results = []

    def log_test(self, test_name: str, success: bool, message: str = "", response_data: Any = None):
        """Log test results."""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        }
        if response_data:
            result["response_data"] = response_data

        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")

    def test_health_endpoint(self):
        """Test the health check endpoint."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log_test("Health Check", True, "API is healthy", data)
                else:
                    self.log_test("Health Check", False, f"API status: {data.get('status')}")
            else:
                self.log_test("Health Check", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Health Check", False, f"Connection error: {str(e)}")

    def test_root_endpoint(self):
        """Test the root endpoint."""
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "message" in data and "endpoints" in data:
                    self.log_test("Root Endpoint", True, "Root endpoint working", data)
                else:
                    self.log_test("Root Endpoint", False, "Missing expected fields")
            else:
                self.log_test("Root Endpoint", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Root Endpoint", False, f"Connection error: {str(e)}")

    def test_search_endpoint(self):
        """Test the search endpoint with various queries."""
        test_queries = [
            "gaming laptops under ₹50K",
            "lightweight laptops",
            "Apple products",
            "smartphones",
            "headphones"
        ]

        for query in test_queries:
            try:
                payload = {"q": query, "limit": 5}
                response = requests.post(
                    f"{self.base_url}/search",
                    json=payload,
                    timeout=15
                )

                if response.status_code == 200:
                    data = response.json()
                    if "products" in data and "total_results" in data:
                        product_count = len(data["products"])
                        self.log_test(
                            f"Search: '{query}'",
                            True,
                            f"Found {product_count} products",
                            {"query": query, "results": product_count}
                        )
                    else:
                        self.log_test(f"Search: '{query}'", False, "Missing expected fields")
                else:
                    self.log_test(f"Search: '{query}'", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"Search: '{query}'", False, f"Error: {str(e)}")

    def test_chat_search_endpoint(self):
        """Test the chat search endpoint with conversational queries."""
        conversation_flow = [
            "gaming laptops under ₹50K",
            "make it lightweight",
            "show me ASUS products",
            "cheaper options"
        ]

        for i, query in enumerate(conversation_flow):
            try:
                payload = {
                    "q": query,
                    "session_id": self.session_id,
                    "limit": 5
                }
                response = requests.post(
                    f"{self.base_url}/chat-search",
                    json=payload,
                    timeout=15
                )

                if response.status_code == 200:
                    data = response.json()
                    if "products" in data and "session_id" in data:
                        product_count = len(data["products"])
                        cached = data.get("cached", False)
                        cache_status = " (cached)" if cached else ""
                        self.log_test(
                            f"Chat Search {i+1}: '{query}'",
                            True,
                            f"Found {product_count} products{cache_status}",
                            {"query": query, "results": product_count, "cached": cached}
                        )
                    else:
                        self.log_test(f"Chat Search {i+1}: '{query}'", False, "Missing expected fields")
                else:
                    self.log_test(f"Chat Search {i+1}: '{query}'", False, f"HTTP {response.status_code}")

                # Small delay between requests
                time.sleep(1)

            except Exception as e:
                self.log_test(f"Chat Search {i+1}: '{query}'", False, f"Error: {str(e)}")

    def test_categories_endpoint(self):
        """Test the categories endpoint."""
        try:
            response = requests.get(f"{self.base_url}/categories", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "categories" in data and "total_categories" in data:
                    category_count = data["total_categories"]
                    self.log_test("Categories", True, f"Found {category_count} categories", data)
                else:
                    self.log_test("Categories", False, "Missing expected fields")
            else:
                self.log_test("Categories", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Categories", False, f"Error: {str(e)}")

    def test_brands_endpoint(self):
        """Test the brands endpoint."""
        try:
            response = requests.get(f"{self.base_url}/brands", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "brands" in data and "total_brands" in data:
                    brand_count = data["total_brands"]
                    self.log_test("Brands", True, f"Found {brand_count} brands", data)
                else:
                    self.log_test("Brands", False, "Missing expected fields")
            else:
                self.log_test("Brands", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Brands", False, f"Error: {str(e)}")

    def test_product_endpoint(self):
        """Test the individual product endpoint."""
        test_product_ids = ["1", "5", "10"]

        for product_id in test_product_ids:
            try:
                response = requests.get(f"{self.base_url}/products/{product_id}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if "id" in data and "title" in data:
                        self.log_test(
                            f"Product {product_id}",
                            True,
                            f"Retrieved: {data.get('title', 'Unknown')}",
                            {"id": product_id, "title": data.get("title")}
                        )
                    else:
                        self.log_test(f"Product {product_id}", False, "Missing expected fields")
                elif response.status_code == 404:
                    self.log_test(f"Product {product_id}", True, "Product not found (expected for invalid ID)")
                else:
                    self.log_test(f"Product {product_id}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"Product {product_id}", False, f"Error: {str(e)}")

    def test_stats_endpoint(self):
        """Test the stats endpoint."""
        try:
            response = requests.get(f"{self.base_url}/stats", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "search_engine" in data and "memory_store" in data:
                    self.log_test("Stats", True, "Stats retrieved successfully", data)
                else:
                    self.log_test("Stats", False, "Missing expected fields")
            else:
                self.log_test("Stats", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_test("Stats", False, f"Error: {str(e)}")

    def run_all_tests(self):
        """Run all tests and generate a report."""
        print("🚀 Starting API Tests...")
        print("=" * 50)

        # Test all endpoints
        self.test_health_endpoint()
        self.test_root_endpoint()
        self.test_search_endpoint()
        self.test_chat_search_endpoint()
        self.test_categories_endpoint()
        self.test_brands_endpoint()
        self.test_product_endpoint()
        self.test_stats_endpoint()

        # Generate summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")

        return passed_tests == total_tests


def main():
    """Main function to run tests."""
    print("Walmart-Style Product Search API Tester")
    print("Make sure the API server is running on http://localhost:8000")
    print()

    # Wait for user confirmation
    input("Press Enter to start testing...")

    tester = APITester()
    success = tester.run_all_tests()

    if success:
        print("\n🎉 All tests passed! The API is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the API server and try again.")

    return success


if __name__ == "__main__":
    main()