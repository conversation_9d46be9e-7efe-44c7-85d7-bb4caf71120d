# Walmart-Style Product Search Engine

A contextual AI-powered product search engine backend that demonstrates natural language query processing and conversational search capabilities. Built with FastAPI, Perplexity API, and Mem0 for intelligent caching.

## 🚀 Features

- **Natural Language Search**: Process queries like "gaming laptops under ₹50K" or "lightweight headphones"
- **Conversational Context**: Handle follow-up queries like "make it lightweight" or "show cheaper options"
- **Intelligent Caching**: Uses Mem0 to cache responses and reduce API calls
- **Semantic Search**: Leverages SentenceTransformers for semantic similarity matching
- **Perplexity Integration**: Uses Perplexity API for advanced query understanding
- **RESTful API**: Clean FastAPI endpoints with automatic documentation
- **Session Management**: Maintains conversation context across multiple queries

## 🛠️ Technology Stack

- **Framework**: FastAPI (Python)
- **AI/ML**: Perplexity API, SentenceTransformers
- **Memory**: Mem0 for intelligent caching
- **Search**: Semantic search with cosine similarity
- **Data**: JSON-based product catalog
- **Server**: Uvicorn ASGI server

## 📁 Project Structure

```
walmart/
├── main.py              # FastAPI application with all endpoints
├── search_engine.py     # Core search logic with Perplexity integration
├── memory_store.py      # Mem0-based caching and session management
├── products.json        # Mock product dataset (Walmart-style)
├── test_api.py         # Comprehensive API testing script
├── requirements.txt     # Python dependencies
├── .env                # Environment variables (API keys)
└── README.md           # This file
```

## 🔧 Installation & Setup

### 1. Clone and Navigate
```bash
cd walmart
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Setup
The `.env` file is already configured with the Perplexity API key:
```
PERPLEXITY_API_KEY=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
```

### 4. Run the Server
```bash
python main.py
```

The server will start on `http://localhost:8000`

## 📚 API Endpoints

### Core Search Endpoints

#### `POST /search`
Basic product search with natural language queries.

**Request:**
```json
{
  "q": "gaming laptops under ₹50K",
  "limit": 10
}
```

**Response:**
```json
{
  "query": "gaming laptops under ₹50K",
  "intent": {
    "category": "Electronics",
    "subcategory": "Gaming Laptops",
    "price_range": {"max": 50000}
  },
  "total_results": 5,
  "returned_results": 5,
  "products": [...],
  "cached": false
}
```

#### `POST /chat-search`
Conversational search with session context.

**Request:**
```json
{
  "q": "make it lightweight",
  "session_id": "unique-session-id",
  "limit": 10
}
```

### Utility Endpoints

- `GET /` - API information and available endpoints
- `GET /health` - Health check with system status
- `GET /categories` - Available product categories
- `GET /brands` - Available brands
- `GET /products/{id}` - Get specific product by ID
- `GET /stats` - API statistics and metrics
- `DELETE /sessions/{id}` - Clear session conversation history

### API Documentation
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_api.py
```

The test script validates:
- All API endpoints
- Search functionality with various queries
- Conversational search flow
- Caching behavior
- Error handling

## 💡 Usage Examples

### Basic Search
```bash
curl -X POST "http://localhost:8000/search" \
  -H "Content-Type: application/json" \
  -d '{"q": "gaming laptops under ₹50K", "limit": 5}'
```

### Conversational Search
```bash
# First query
curl -X POST "http://localhost:8000/chat-search" \
  -H "Content-Type: application/json" \
  -d '{"q": "gaming laptops under ₹50K", "session_id": "session123", "limit": 5}'

# Follow-up query
curl -X POST "http://localhost:8000/chat-search" \
  -H "Content-Type: application/json" \
  -d '{"q": "make it lightweight", "session_id": "session123", "limit": 5}'
```

## 🔍 Search Capabilities

### Supported Query Types
- **Category-based**: "laptops", "smartphones", "headphones"
- **Brand-specific**: "Apple products", "ASUS laptops"
- **Price-filtered**: "under ₹50K", "between ₹30K and ₹60K"
- **Feature-based**: "lightweight", "gaming", "wireless"
- **Conversational**: "make it cheaper", "show me alternatives"

### Query Understanding
The system uses Perplexity API to extract structured intent from natural language:
- Intent classification (search vs. refinement)
- Category and subcategory detection
- Price range extraction
- Feature and brand identification
- Contextual understanding for follow-ups

## 🎯 Product Dataset

The `products.json` file contains 15 sample products across categories:
- **Electronics**: Gaming laptops, smartphones, tablets, audio, TVs
- **Fashion**: Shoes from Nike and Adidas
- **Home & Kitchen**: Appliances

Each product includes:
- ID, title, brand, category, subcategory
- Price in INR
- Detailed description and features
- Weight, rating, reviews, availability

## 🚀 Performance Features

### Intelligent Caching
- **Query Caching**: Identical queries return cached results
- **Session Context**: Conversation history maintained per session
- **TTL Management**: 24-hour cache expiration
- **Memory Optimization**: Limited to 100 cached queries

### Semantic Search
- **Embedding-based**: Uses SentenceTransformers for semantic similarity
- **Precomputed Embeddings**: Fast search with precomputed product vectors
- **Fallback Support**: Works even if semantic search fails

## 🔧 Configuration

### Environment Variables
- `PERPLEXITY_API_KEY`: Your Perplexity API key (already configured)

### Customization Options
- **Cache TTL**: Modify `cache_ttl` in `memory_store.py`
- **Max Cache Size**: Adjust `max_cache_size` in `memory_store.py`
- **Search Limits**: Configure default limits in API endpoints
- **Product Data**: Replace `products.json` with your own dataset

## 🛡️ Error Handling

The API includes comprehensive error handling:
- **Graceful Degradation**: Falls back to basic search if AI services fail
- **Timeout Management**: Reasonable timeouts for external API calls
- **Validation**: Input validation with clear error messages
- **Logging**: Detailed logging for debugging and monitoring

## 📈 Monitoring

### Health Check
```bash
curl http://localhost:8000/health
```

Returns system status including:
- Search engine health
- Memory store statistics
- API availability status

### Statistics
```bash
curl http://localhost:8000/stats
```

Provides comprehensive metrics:
- Total products and categories
- Cache performance
- API usage statistics

## 🚀 Production Considerations

### Security
- Configure CORS origins for production
- Add authentication/authorization as needed
- Secure API key management

### Scaling
- Add database backend for larger product catalogs
- Implement Redis for distributed caching
- Use load balancing for multiple instances

### Monitoring
- Add application performance monitoring
- Implement structured logging
- Set up health check endpoints for orchestration

## 🤝 Contributing

This is a demo project showcasing AI-powered search capabilities. Feel free to:
- Extend the product dataset
- Add new search features
- Improve the query understanding logic
- Enhance the caching strategy

## 📄 License

This project is for demonstration purposes. Use the code as a reference for building production search systems.

---

**Built with ❤️ using FastAPI, Perplexity API, and Mem0**