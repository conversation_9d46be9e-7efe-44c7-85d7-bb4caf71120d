// API service for connecting to the FastAPI backend

const API_BASE_URL = 'http://localhost:8000';

export interface Product {
  id: string;
  title: string;
  category: string;
  subcategory: string;
  brand: string;
  price: number;
  currency: string;
  description: string;
  features: string[];
  weight: string;
  rating: number;
  reviews: number;
  availability: string;
}

export interface SearchResponse {
  query: string;
  intent: Record<string, any>;
  total_results: number;
  returned_results: number;
  products: Product[];
  search_metadata: Record<string, any>;
  cached: boolean;
  session_id?: string;
}

export interface SearchRequest {
  q: string;
  limit?: number;
}

export interface ChatSearchRequest {
  q: string;
  session_id: string;
  limit?: number;
}

export interface ApiError {
  detail: string;
  message?: string;
}

class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData: ApiError = await response.json().catch(() => ({
        detail: `HTTP ${response.status}: ${response.statusText}`
      }));
      throw new Error(errorData.detail || errorData.message || 'An error occurred');
    }
    return response.json();
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/health`);
    return this.handleResponse(response);
  }

  // Search products
  async searchProducts(request: SearchRequest): Promise<SearchResponse> {
    const response = await fetch(`${this.baseUrl}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    return this.handleResponse<SearchResponse>(response);
  }

  // Chat search with session context
  async chatSearchProducts(request: ChatSearchRequest): Promise<SearchResponse> {
    const response = await fetch(`${this.baseUrl}/chat-search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    return this.handleResponse<SearchResponse>(response);
  }

  // Get categories
  async getCategories(): Promise<{ categories: string[]; total_categories: number }> {
    const response = await fetch(`${this.baseUrl}/categories`);
    return this.handleResponse(response);
  }

  // Get brands
  async getBrands(): Promise<{ brands: string[]; total_brands: number }> {
    const response = await fetch(`${this.baseUrl}/brands`);
    return this.handleResponse(response);
  }

  // Get product by ID
  async getProduct(productId: string): Promise<Product> {
    const response = await fetch(`${this.baseUrl}/products/${productId}`);
    return this.handleResponse<Product>(response);
  }

  // Clear session
  async clearSession(sessionId: string): Promise<{ message: string }> {
    const response = await fetch(`${this.baseUrl}/sessions/${sessionId}`, {
      method: 'DELETE',
    });
    return this.handleResponse(response);
  }

  // Get API stats
  async getStats(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/stats`);
    return this.handleResponse(response);
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Utility function to generate session ID
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Utility function to format price
export const formatPrice = (price: number, currency: string = 'INR'): string => {
  if (currency === 'INR') {
    return `₹${price.toLocaleString('en-IN')}`;
  }
  return `${currency} ${price.toLocaleString()}`;
};

// Utility function to get star rating display
export const getStarRating = (rating: number): string => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return '★'.repeat(fullStars) + 
         (hasHalfStar ? '☆' : '') + 
         '☆'.repeat(emptyStars);
};

export default apiService;
