"""
Search Engine Core with Perplexity API integration and semantic search.
Handles natural language query understanding and product filtering.
"""

import json
import logging
import os
import re
from typing import Dict, List, Optional, Any, Tuple
import requests
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SearchEngine:
    """
    Core search engine with Perplexity API integration and semantic search capabilities.
    """

    def __init__(self, products_file: str = "products.json"):
        """
        Initialize the search engine.

        Args:
            products_file: Path to the products JSON file
        """
        self.products_file = products_file
        self.products = self._load_products()
        self.perplexity_api_key = os.getenv("PERPLEXITY_API_KEY")

        # Initialize sentence transformer for semantic search
        try:
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            self._precompute_embeddings()
            logger.info("Search engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize sentence transformer: {e}")
            self.sentence_model = None
            self.product_embeddings = None

    def _load_products(self) -> List[Dict[str, Any]]:
        """Load products from JSON file."""
        try:
            with open(self.products_file, 'r', encoding='utf-8') as f:
                products = json.load(f)
            logger.info(f"Loaded {len(products)} products")
            return products
        except Exception as e:
            logger.error(f"Failed to load products: {e}")
            return []

    def _precompute_embeddings(self):
        """Precompute embeddings for all products for faster semantic search."""
        if not self.sentence_model or not self.products:
            return

        try:
            # Create text representations of products
            product_texts = []
            for product in self.products:
                text = f"{product['title']} {product['brand']} {product['description']} {' '.join(product['features'])}"
                product_texts.append(text)

            # Compute embeddings
            self.product_embeddings = self.sentence_model.encode(product_texts)
            logger.info("Product embeddings precomputed successfully")
        except Exception as e:
            logger.error(f"Failed to precompute embeddings: {e}")
            self.product_embeddings = None

    def _call_perplexity_api(self, query: str, context: str = "") -> Dict[str, Any]:
        """
        Call Perplexity API to understand and refine the query.

        Args:
            query: The natural language query
            context: Additional context from conversation history

        Returns:
            Structured intent extracted from the query
        """
        if not self.perplexity_api_key:
            logger.warning("Perplexity API key not found, using fallback parsing")
            return self._fallback_query_parsing(query)

        try:
            url = "https://api.perplexity.ai/chat/completions"

            # Construct the prompt for query understanding
            system_prompt = """You are a product search query analyzer. Extract structured information from natural language queries for an e-commerce search.

Return a JSON object with these fields:
- "intent": main search intent (e.g., "product_search", "filter_refinement")
- "category": product category if mentioned (e.g., "Electronics", "Fashion", "Home & Kitchen")
- "subcategory": specific subcategory if mentioned (e.g., "Gaming Laptops", "Smartphones")
- "brand": brand name if mentioned
- "price_range": {"min": number, "max": number} if price mentioned
- "features": list of specific features or requirements mentioned
- "keywords": list of important search keywords
- "filters": any specific filters mentioned (weight, rating, etc.)

Examples:
Query: "gaming laptops under ₹50K"
Response: {"intent": "product_search", "category": "Electronics", "subcategory": "Gaming Laptops", "price_range": {"max": 50000}, "keywords": ["gaming", "laptops"], "features": []}

Query: "make it lightweight"
Response: {"intent": "filter_refinement", "features": ["lightweight"], "keywords": ["lightweight"], "filters": {"weight": "light"}}"""

            user_prompt = f"Query: {query}"
            if context:
                user_prompt += f"\nContext: {context}"

            payload = {
                "model": "llama-3.1-sonar-small-128k-online",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 500
            }

            headers = {
                "Authorization": f"Bearer {self.perplexity_api_key}",
                "Content-Type": "application/json"
            }

            response = requests.post(url, json=payload, headers=headers, timeout=10)
            response.raise_for_status()

            result = response.json()
            content = result['choices'][0]['message']['content']

            # Try to extract JSON from the response
            try:
                # Look for JSON in the response
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    parsed_intent = json.loads(json_match.group())
                    logger.info(f"Perplexity API parsed query successfully")
                    return parsed_intent
                else:
                    logger.warning("No JSON found in Perplexity response, using fallback")
                    return self._fallback_query_parsing(query)
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON from Perplexity response, using fallback")
                return self._fallback_query_parsing(query)

        except Exception as e:
            logger.error(f"Perplexity API call failed: {e}")
            return self._fallback_query_parsing(query)

    def _fallback_query_parsing(self, query: str) -> Dict[str, Any]:
        """
        Fallback query parsing when Perplexity API is not available.

        Args:
            query: The natural language query

        Returns:
            Basic structured intent extracted from the query
        """
        query_lower = query.lower()

        # Extract price range
        price_range = {}
        price_patterns = [
            r'under\s*₹?(\d+)k?',
            r'below\s*₹?(\d+)k?',
            r'less\s*than\s*₹?(\d+)k?',
            r'₹?(\d+)k?\s*to\s*₹?(\d+)k?',
            r'between\s*₹?(\d+)k?\s*and\s*₹?(\d+)k?'
        ]

        for pattern in price_patterns:
            match = re.search(pattern, query_lower)
            if match:
                if len(match.groups()) == 1:
                    price = int(match.group(1))
                    if 'k' in match.group(0):
                        price *= 1000
                    price_range['max'] = price
                elif len(match.groups()) == 2:
                    min_price = int(match.group(1))
                    max_price = int(match.group(2))
                    if 'k' in match.group(0):
                        min_price *= 1000
                        max_price *= 1000
                    price_range['min'] = min_price
                    price_range['max'] = max_price
                break

        # Extract category and subcategory
        category_mapping = {
            'gaming laptop': ('Electronics', 'Gaming Laptops'),
            'laptop': ('Electronics', None),  # Don't restrict subcategory for general laptop search
            'phone': ('Electronics', 'Smartphones'),
            'smartphone': ('Electronics', 'Smartphones'),
            'tablet': ('Electronics', 'Tablets'),
            'headphone': ('Electronics', 'Audio'),
            'tv': ('Electronics', 'TVs'),
            'shoe': ('Fashion', 'Shoes'),
            'appliance': ('Home & Kitchen', 'Appliances')
        }

        category = None
        subcategory = None
        # Check for more specific matches first
        for keyword, (cat, subcat) in sorted(category_mapping.items(), key=lambda x: len(x[0]), reverse=True):
            if keyword in query_lower:
                category = cat
                subcategory = subcat
                break

        # Extract brand
        brand_mapping = {
            'asus': 'ASUS', 'hp': 'HP', 'lenovo': 'Lenovo', 'acer': 'Acer',
            'msi': 'MSI', 'dell': 'Dell', 'apple': 'Apple', 'samsung': 'Samsung',
            'sony': 'Sony', 'lg': 'LG', 'nike': 'Nike', 'adidas': 'Adidas'
        }
        brand = None
        for b_key, b_value in brand_mapping.items():
            if b_key in query_lower:
                brand = b_value
                break

        # Extract features
        feature_keywords = ['lightweight', 'gaming', 'wireless', 'noise canceling', 'smart', '4k', 'hdr']
        features = [f for f in feature_keywords if f in query_lower]

        # Determine intent
        intent = "filter_refinement" if any(word in query_lower for word in ['make', 'show', 'filter', 'find']) else "product_search"

        # Extract keywords
        keywords = query.lower().split()

        return {
            'intent': intent,
            'category': category,
            'subcategory': subcategory,
            'brand': brand,
            'price_range': price_range,
            'features': features,
            'keywords': keywords,
            'filters': {}
        }

    def _filter_products(self, intent: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter products based on extracted intent.

        Args:
            intent: Structured intent from query parsing

        Returns:
            List of filtered products
        """
        filtered_products = self.products.copy()

        # Filter by category
        if intent.get('category'):
            filtered_products = [p for p in filtered_products if p.get('category') == intent['category']]

        # Filter by subcategory
        if intent.get('subcategory'):
            filtered_products = [p for p in filtered_products if p.get('subcategory') == intent['subcategory']]

        # Filter by brand
        if intent.get('brand'):
            brand_filter = intent['brand'].lower()
            filtered_products = [p for p in filtered_products if p.get('brand', '').lower() == brand_filter]

        # Filter by price range
        price_range = intent.get('price_range', {})
        if price_range:
            if 'min' in price_range:
                filtered_products = [p for p in filtered_products if p.get('price', 0) >= price_range['min']]
            if 'max' in price_range:
                filtered_products = [p for p in filtered_products if p.get('price', 0) <= price_range['max']]

        # Filter by features
        features = intent.get('features', [])
        if features:
            for feature in features:
                filtered_products = [
                    p for p in filtered_products
                    if any(feature.lower() in f.lower() for f in p.get('features', []))
                ]

        return filtered_products

    def _semantic_search(self, query: str, products: List[Dict[str, Any]], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Perform semantic search on products using sentence transformers.

        Args:
            query: The search query
            products: List of products to search
            limit: Maximum number of results to return

        Returns:
            List of products ranked by semantic similarity
        """
        # Temporarily disable semantic search to debug
        logger.info("Semantic search temporarily disabled for debugging")
        return products[:limit]

    def search(self, query: str, context: str = "", limit: int = 10) -> Dict[str, Any]:
        """
        Main search method that combines Perplexity API understanding with semantic search.

        Args:
            query: The natural language search query
            context: Additional context from conversation history
            limit: Maximum number of results to return

        Returns:
            Search results with products and metadata
        """
        try:
            logger.info(f"Processing search query: {query}")

            # Parse query using Perplexity API or fallback
            intent = self._call_perplexity_api(query, context)

            # Filter products based on intent
            filtered_products = self._filter_products(intent)

            # Perform semantic search on filtered products
            ranked_products = self._semantic_search(query, filtered_products, limit)

            # Prepare response
            response = {
                'query': query,
                'intent': intent,
                'total_results': len(filtered_products),
                'returned_results': len(ranked_products),
                'products': ranked_products,
                'search_metadata': {
                    'used_perplexity': bool(self.perplexity_api_key),
                    'used_semantic_search': bool(self.sentence_model),
                    'filters_applied': {
                        'category': intent.get('category'),
                        'subcategory': intent.get('subcategory'),
                        'brand': intent.get('brand'),
                        'price_range': intent.get('price_range'),
                        'features': intent.get('features')
                    }
                }
            }

            logger.info(f"Search completed: {len(ranked_products)} results returned")
            return response

        except Exception as e:
            logger.error(f"Search failed: {e}")
            return {
                'query': query,
                'intent': {},
                'total_results': 0,
                'returned_results': 0,
                'products': [],
                'error': str(e),
                'search_metadata': {
                    'used_perplexity': False,
                    'used_semantic_search': False,
                    'filters_applied': {}
                }
            }

    def get_product_by_id(self, product_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific product by ID.

        Args:
            product_id: The product ID

        Returns:
            Product dict or None if not found
        """
        for product in self.products:
            if product['id'] == product_id:
                return product
        return None

    def get_categories(self) -> Dict[str, List[str]]:
        """
        Get all available categories and subcategories.

        Returns:
            Dict mapping categories to their subcategories
        """
        categories = {}
        for product in self.products:
            category = product.get('category')
            subcategory = product.get('subcategory')

            if category:
                if category not in categories:
                    categories[category] = []
                if subcategory and subcategory not in categories[category]:
                    categories[category].append(subcategory)

        return categories

    def get_brands(self) -> List[str]:
        """
        Get all available brands.

        Returns:
            List of unique brands
        """
        brands = set()
        for product in self.products:
            brand = product.get('brand')
            if brand:
                brands.add(brand)

        return sorted(list(brands))

    def get_search_stats(self) -> Dict[str, Any]:
        """
        Get search engine statistics and health information.

        Returns:
            Dict with search engine statistics
        """
        return {
            'total_products': len(self.products),
            'categories': len(self.get_categories()),
            'brands': len(self.get_brands()),
            'perplexity_api_available': bool(self.perplexity_api_key),
            'semantic_search_available': bool(self.sentence_model),
            'embeddings_precomputed': bool(self.product_embeddings is not None),
            'status': 'healthy' if self.products else 'no_products'
        }