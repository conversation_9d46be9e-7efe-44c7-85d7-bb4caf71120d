
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, Heart } from 'lucide-react';

const FeaturedProducts = () => {
  const products = [
    {
      id: 1,
      name: "Apple iPhone 15",
      price: 799,
      originalPrice: 899,
      rating: 4.8,
      reviews: 1284,
      image: "📱",
      badge: "Rollback"
    },
    {
      id: 2,
      name: "Samsung 65\" 4K Smart TV",
      price: 498,
      originalPrice: 698,
      rating: 4.6,
      reviews: 892,
      image: "📺",
      badge: "Best Seller"
    },
    {
      id: 3,
      name: "Ninja Air Fryer",
      price: 89,
      originalPrice: 129,
      rating: 4.7,
      reviews: 2341,
      image: "🍳",
      badge: "Top Pick"
    },
    {
      id: 4,
      name: "Dyson V15 Vacuum",
      price: 549,
      originalPrice: 649,
      rating: 4.9,
      reviews: 567,
      image: "🧹",
      badge: "Free Shipping"
    },
    {
      id: 5,
      name: "KitchenAid Stand Mixer",
      price: 299,
      originalPrice: 379,
      rating: 4.8,
      reviews: 1456,
      image: "🥧",
      badge: "Reduced Price"
    },
    {
      id: 6,
      name: "Apple MacBook Air",
      price: 999,
      originalPrice: 1199,
      rating: 4.9,
      reviews: 734,
      image: "💻",
      badge: "Rollback"
    }
  ];

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Featured Products</h2>
          <Button variant="outline" className="text-walmart-blue border-walmart-blue hover:bg-walmart-blue hover:text-white">
            View all
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {products.map((product) => (
            <Card key={product.id} className="cursor-pointer hover:shadow-lg transition-all duration-300 group">
              <CardContent className="p-4">
                {/* Badge */}
                <div className="flex justify-between items-start mb-3">
                  <span className="bg-walmart-yellow text-black text-xs font-semibold px-2 py-1 rounded">
                    {product.badge}
                  </span>
                  <Button variant="ghost" size="sm" className="p-1 hover:bg-gray-100">
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Product Image */}
                <div className="text-6xl text-center mb-4 group-hover:scale-105 transition-transform duration-300">
                  {product.image}
                </div>
                
                {/* Product Info */}
                <h3 className="font-medium text-sm mb-2 line-clamp-2">{product.name}</h3>
                
                {/* Rating */}
                <div className="flex items-center mb-2">
                  <div className="flex text-walmart-yellow">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className={`w-3 h-3 ${i < Math.floor(product.rating) ? 'fill-current' : ''}`} />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500 ml-1">({product.reviews})</span>
                </div>
                
                {/* Price */}
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-lg font-bold text-walmart-blue">${product.price}</span>
                  <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                </div>
                
                {/* Add to Cart Button */}
                <Button className="w-full bg-walmart-blue hover:bg-walmart-darkblue text-white text-sm">
                  Add to cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
