"""
Memory Store Module using Mem0 for caching queries and session management.
Handles query caching, session context, and conversation history.
"""

import hashlib
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MemoryStore:
    """
    Memory store for caching search queries and managing session context.
    Uses Mem0 for persistent memory and caching.
    """

    def __init__(self):
        """Initialize the memory store with simple in-memory caching."""
        try:
            # Initialize simple in-memory cache
            self.query_cache = {}  # Cache for query responses
            self.session_cache = {}  # Cache for session contexts
            self.cache_ttl = timedelta(hours=24)  # Cache TTL of 24 hours
            self.max_cache_size = 100  # Maximum cached queries
            logger.info("Memory store initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize memory store: {e}")
            raise

    def _generate_cache_key(self, query: str) -> str:
        """Generate a unique cache key for a query."""
        return hashlib.md5(query.lower().strip().encode()).hexdigest()

    def get_cached_response(self, query: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached response for a query if it exists and is still valid.

        Args:
            query: The search query

        Returns:
            Cached response dict or None if not found/expired
        """
        try:
            cache_key = self._generate_cache_key(query)

            # Check if query is in cache
            if cache_key in self.query_cache:
                cached_data = self.query_cache[cache_key]

                # Check if cache is still valid
                if 'timestamp' in cached_data:
                    cache_time = datetime.fromisoformat(cached_data['timestamp'])
                    if datetime.now() - cache_time < self.cache_ttl:
                        logger.info(f"Cache hit for query: {query[:50]}...")
                        return cached_data.get('response')
                    else:
                        logger.info(f"Cache expired for query: {query[:50]}...")
                        # Remove expired cache
                        del self.query_cache[cache_key]

            logger.info(f"Cache miss for query: {query[:50]}...")
            return None

        except Exception as e:
            logger.error(f"Error retrieving cached response: {e}")
            return None

    def cache_response(self, query: str, response: Dict[str, Any]) -> bool:
        """
        Cache a response for a query.

        Args:
            query: The search query
            response: The response to cache

        Returns:
            True if cached successfully, False otherwise
        """
        try:
            cache_key = self._generate_cache_key(query)

            # Clean up old cache entries if we're at the limit
            if len(self.query_cache) >= self.max_cache_size:
                # Remove oldest entries
                sorted_cache = sorted(
                    self.query_cache.items(),
                    key=lambda x: x[1].get('timestamp', ''),
                    reverse=False
                )
                # Remove oldest 20% of entries
                remove_count = max(1, len(sorted_cache) // 5)
                for i in range(remove_count):
                    del self.query_cache[sorted_cache[i][0]]

            # Prepare cache data
            cache_data = {
                'cache_key': cache_key,
                'query': query,
                'response': response,
                'timestamp': datetime.now().isoformat(),
                'type': 'query_cache'
            }

            # Store in cache
            self.query_cache[cache_key] = cache_data

            logger.info(f"Cached response for query: {query[:50]}...")
            return True

        except Exception as e:
            logger.error(f"Error caching response: {e}")
            return False

    def _remove_cached_response(self, cache_key: str) -> bool:
        """Remove a cached response by cache key."""
        try:
            # Note: Mem0 doesn't have direct delete by metadata,
            # so we'll let it naturally expire or be overwritten
            logger.info(f"Marked cache for removal: {cache_key}")
            return True
        except Exception as e:
            logger.error(f"Error removing cached response: {e}")
            return False

    def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """
        Retrieve session context including conversation history.

        Args:
            session_id: Unique session identifier

        Returns:
            Session context dict with conversation history
        """
        try:
            # Check if session exists in cache
            if session_id in self.session_cache:
                session_data = self.session_cache[session_id]
                logger.info(f"Retrieved session context for: {session_id}")
                return session_data

            # Return empty context for new session
            logger.info(f"New session created: {session_id}")
            new_context = {
                'session_id': session_id,
                'conversation_history': [],
                'last_query': '',
                'context_summary': '',
                'timestamp': datetime.now().isoformat()
            }
            self.session_cache[session_id] = new_context
            return new_context

        except Exception as e:
            logger.error(f"Error retrieving session context: {e}")
            return {
                'session_id': session_id,
                'conversation_history': [],
                'last_query': '',
                'context_summary': '',
                'timestamp': datetime.now().isoformat()
            }

    def update_session_context(self, session_id: str, query: str,
                             response: Dict[str, Any],
                             context_summary: str = '') -> bool:
        """
        Update session context with new query and response.

        Args:
            session_id: Unique session identifier
            query: The search query
            response: The search response
            context_summary: Summary of conversation context

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            # Get current session context
            current_context = self.get_session_context(session_id)

            # Add new interaction to conversation history
            new_interaction = {
                'query': query,
                'response': response,
                'timestamp': datetime.now().isoformat()
            }

            # Update conversation history (keep last 10 interactions)
            conversation_history = current_context.get('conversation_history', [])
            conversation_history.append(new_interaction)
            if len(conversation_history) > 10:
                conversation_history = conversation_history[-10:]

            # Prepare session data
            session_data = {
                'session_id': session_id,
                'conversation_history': conversation_history,
                'last_query': query,
                'context_summary': context_summary,
                'timestamp': datetime.now().isoformat(),
                'type': 'session_context'
            }

            # Store in session cache
            self.session_cache[session_id] = session_data

            logger.info(f"Updated session context for: {session_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating session context: {e}")
            return False

    def clear_session(self, session_id: str) -> bool:
        """
        Clear session context and conversation history.

        Args:
            session_id: Unique session identifier

        Returns:
            True if cleared successfully, False otherwise
        """
        try:
            if session_id in self.session_cache:
                del self.session_cache[session_id]
            logger.info(f"Session cleared: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing session: {e}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics and health information.

        Returns:
            Dict with cache statistics
        """
        try:
            cache_count = len(self.query_cache)
            session_count = len(self.session_cache)

            return {
                'cache_count': cache_count,
                'session_count': session_count,
                'max_cache_size': self.max_cache_size,
                'cache_ttl_hours': self.cache_ttl.total_seconds() / 3600,
                'status': 'healthy'
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                'cache_count': 0,
                'session_count': 0,
                'max_cache_size': self.max_cache_size,
                'cache_ttl_hours': self.cache_ttl.total_seconds() / 3600,
                'status': 'error',
                'error': str(e)
            }