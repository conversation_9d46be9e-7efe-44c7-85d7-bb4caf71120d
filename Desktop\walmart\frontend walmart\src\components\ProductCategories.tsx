
import { Card, CardContent } from '@/components/ui/card';

const ProductCategories = () => {
  const categories = [
    {
      name: "Electronics",
      image: "🖥️",
      discount: "Up to 50% off"
    },
    {
      name: "Home",
      image: "🏠",
      discount: "Starting $5"
    },
    {
      name: "Fashion",
      image: "👕",
      discount: "Up to 65% off"
    },
    {
      name: "Grocery",
      image: "🛒",
      discount: "Everyday low prices"
    },
    {
      name: "Auto",
      image: "🚗",
      discount: "Free pickup"
    },
    {
      name: "Pharmacy",
      image: "💊",
      discount: "$4 prescriptions"
    },
    {
      name: "Baby",
      image: "👶",
      discount: "Up to 30% off"
    },
    {
      name: "Sports",
      image: "⚽",
      discount: "Great deals"
    }
  ];

  return (
    <section className="py-12 bg-walmart-gray">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-3xl font-bold mb-8 text-center">Shop by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {categories.map((category, index) => (
            <Card key={index} className="cursor-pointer hover:shadow-lg transition-shadow duration-300 bg-white">
              <CardContent className="p-4 text-center">
                <div className="text-4xl mb-3">{category.image}</div>
                <h3 className="font-semibold text-sm mb-2">{category.name}</h3>
                <p className="text-xs text-walmart-blue font-medium">{category.discount}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductCategories;
