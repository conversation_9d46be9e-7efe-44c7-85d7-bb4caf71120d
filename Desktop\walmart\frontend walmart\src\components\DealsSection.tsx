
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock } from 'lucide-react';
import { useState, useEffect } from 'react';

const DealsSection = () => {
  const [timeLeft, setTimeLeft] = useState({
    hours: 23,
    minutes: 45,
    seconds: 30
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 };
        }
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const deals = [
    {
      title: "Flash Deal",
      subtitle: "Limited time offer",
      discount: "70% OFF",
      product: "Wireless Headphones",
      price: 29.99,
      originalPrice: 99.99,
      image: "🎧",
      urgency: "Only 12 left!"
    },
    {
      title: "Deal of the Day",
      subtitle: "Today only",
      discount: "50% OFF",
      product: "Smart Watch",
      price: 149.99,
      originalPrice: 299.99,
      image: "⌚",
      urgency: "Selling fast!"
    },
    {
      title: "Weekend Special",
      subtitle: "This weekend only",
      discount: "40% OFF",
      product: "Gaming Console",
      price: 299.99,
      originalPrice: 499.99,
      image: "🎮",
      urgency: "Limited stock"
    }
  ];

  return (
    <section className="py-12 bg-gradient-to-r from-walmart-blue to-walmart-darkblue text-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-4xl font-bold mb-4">Today's Top Deals</h2>
          <div className="flex items-center justify-center space-x-4 text-walmart-yellow">
            <Clock className="w-6 h-6" />
            <span className="text-lg">Deals end in:</span>
            <div className="flex space-x-4 text-2xl font-bold">
              <div className="bg-walmart-yellow text-black px-3 py-2 rounded">
                {String(timeLeft.hours).padStart(2, '0')}
              </div>
              <span>:</span>
              <div className="bg-walmart-yellow text-black px-3 py-2 rounded">
                {String(timeLeft.minutes).padStart(2, '0')}
              </div>
              <span>:</span>
              <div className="bg-walmart-yellow text-black px-3 py-2 rounded">
                {String(timeLeft.seconds).padStart(2, '0')}
              </div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {deals.map((deal, index) => (
            <Card key={index} className="bg-white text-black hover:shadow-xl transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="bg-walmart-yellow text-black text-lg font-bold px-3 py-1 rounded-full inline-block mb-4">
                    {deal.discount}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{deal.title}</h3>
                  <p className="text-gray-600 mb-4">{deal.subtitle}</p>
                  
                  <div className="text-6xl mb-4">{deal.image}</div>
                  
                  <h4 className="font-semibold mb-3">{deal.product}</h4>
                  
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <span className="text-2xl font-bold text-walmart-blue">${deal.price}</span>
                    <span className="text-gray-500 line-through">${deal.originalPrice}</span>
                  </div>
                  
                  <p className="text-red-600 font-medium text-sm mb-4">{deal.urgency}</p>
                  
                  <Button className="w-full bg-walmart-blue hover:bg-walmart-darkblue text-white font-semibold py-3">
                    Shop Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DealsSection;
