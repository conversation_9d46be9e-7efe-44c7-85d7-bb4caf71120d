
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const slides = [
    {
      title: "Save big on gifts",
      subtitle: "Shop deals on everything they want",
      buttonText: "Shop now",
      bgColor: "bg-gradient-to-r from-walmart-blue to-walmart-darkblue",
      textColor: "text-white"
    },
    {
      title: "Cyber Monday deals",
      subtitle: "Don't miss out on our biggest sale",
      buttonText: "See deals",
      bgColor: "bg-gradient-to-r from-walmart-yellow to-walmart-orange",
      textColor: "text-black"
    },
    {
      title: "Grocery pickup & delivery",
      subtitle: "Fresh groceries at low prices",
      buttonText: "Start shopping",
      bgColor: "bg-gradient-to-r from-walmart-green to-green-600",
      textColor: "text-white"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <section className="relative h-96 overflow-hidden">
      <div className="flex transition-transform duration-500 ease-in-out h-full"
           style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
        {slides.map((slide, index) => (
          <div key={index} className={`min-w-full h-full ${slide.bgColor} flex items-center justify-center`}>
            <div className="text-center max-w-2xl px-4">
              <h2 className={`text-5xl font-bold mb-4 ${slide.textColor}`}>
                {slide.title}
              </h2>
              <p className={`text-xl mb-8 ${slide.textColor}`}>
                {slide.subtitle}
              </p>
              <Button 
                size="lg" 
                className="bg-white text-walmart-blue hover:bg-gray-100 font-semibold px-8 py-3 rounded-full"
              >
                {slide.buttonText}
              </Button>
            </div>
          </div>
        ))}
      </div>
      
      {/* Navigation arrows */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-black rounded-full p-2"
        onClick={prevSlide}
      >
        <ChevronLeft className="w-6 h-6" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-black rounded-full p-2"
        onClick={nextSlide}
      >
        <ChevronRight className="w-6 h-6" />
      </Button>
      
      {/* Slide indicators */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;
